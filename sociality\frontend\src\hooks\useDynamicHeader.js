/**
 * Dynamic Header Hook
 * Provides responsive header behavior for all smartphone sizes
 */

import { useState, useEffect, useCallback } from 'react';
import {
  getCurrentDeviceCategory,
  isMobileDevice,
  shouldUseBottomNavigation,
  getDynamicSpacing,
  getDynamicButtonSize,
  getDynamicLogoSize,
  getSafeAreaInsets,
  hasNotch,
  getOrientationAdjustments
} from '../utils/responsiveBreakpoints';

export const useDynamicHeader = () => {
  // State for dynamic values
  const [deviceCategory, setDeviceCategory] = useState(getCurrentDeviceCategory());
  const [isLogoVisible, setIsLogoVisible] = useState(true);
  const [lastScrollY, setLastScrollY] = useState(0);
  const [orientation, setOrientation] = useState(window.innerWidth > window.innerHeight ? 'landscape' : 'portrait');
  
  // Update device category on resize
  const updateDeviceCategory = useCallback(() => {
    const newCategory = getCurrentDeviceCategory();
    const newOrientation = window.innerWidth > window.innerHeight ? 'landscape' : 'portrait';
    
    setDeviceCategory(newCategory);
    setOrientation(newOrientation);
  }, []);
  
  // Scroll handler for logo visibility
  const handleScroll = useCallback(() => {
    if (!isMobileDevice()) return;
    
    const currentScrollY = window.scrollY;
    const scrollThreshold = deviceCategory === 'xs' ? 30 : deviceCategory === 'sm' ? 40 : 50;
    
    // Hide logo when scrolling down past threshold, show when scrolling up or near top
    if (currentScrollY > scrollThreshold && currentScrollY > lastScrollY) {
      setIsLogoVisible(false);
    } else if (currentScrollY < lastScrollY || currentScrollY <= scrollThreshold * 0.6) {
      setIsLogoVisible(true);
    }
    
    setLastScrollY(currentScrollY);
  }, [lastScrollY, deviceCategory]);
  
  // Set up event listeners
  useEffect(() => {
    const handleResize = () => {
      updateDeviceCategory();
    };
    
    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleResize);
    window.addEventListener('scroll', handleScroll, { passive: true });
    
    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleResize);
      window.removeEventListener('scroll', handleScroll);
    };
  }, [updateDeviceCategory, handleScroll]);
  
  // Get dynamic values based on current device
  const spacing = getDynamicSpacing();
  const buttonSize = getDynamicButtonSize();
  const logoSize = getDynamicLogoSize();
  const safeArea = getSafeAreaInsets();
  const orientationAdjustments = getOrientationAdjustments();
  
  // Logo positioning
  const getLogoPosition = () => {
    const baseTop = orientationAdjustments.logoTop;
    
    return {
      top: `${baseTop}px`,
      left: '50%',
      transform: `translateX(-50%) ${isLogoVisible ? 'translateY(0)' : 'translateY(-100px)'}`,
      opacity: isLogoVisible ? 1 : 0,
      transition: deviceCategory === 'xs' ? 
        'all 0.2s ease-in-out' : 
        'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
    };
  };
  
  // Navigation positioning
  const getNavigationPosition = () => {
    const baseBottom = orientationAdjustments.navBottom;

    // Use the new shouldUseBottomNavigation function for better device detection
    const useBottomNav = shouldUseBottomNavigation();

    // All mobile devices (including large ones up to 768px) use bottom navigation
    if (useBottomNav) {
      return {
        bottom: '0px',
        left: '0px',
        right: '0px',
        transform: 'none',
        width: '100%',
        gap: `${orientationAdjustments.spacing}px`,
        padding: `${spacing.padding}px ${spacing.padding}px`,
        paddingBottom: `${Math.max(spacing.padding, baseBottom)}px`,
        justifyContent: 'center',
        borderRadius: '0px'
      };
    }

    // For desktop and tablets (768px+), use left-side vertical navigation
    return {
      left: '16px',
      top: '50%',
      transform: 'translateY(-50%)',
      bottom: 'auto',
      right: 'auto',
      width: 'auto',
      gap: `${orientationAdjustments.spacing}px`,
      padding: `${spacing.padding * 1.5}px ${spacing.padding}px`,
      justifyContent: 'flex-start',
      borderRadius: '12px'
    };
  };
  
  // Button styles
  const getButtonStyles = () => ({
    minHeight: orientationAdjustments.buttonSize.minH,
    minWidth: orientationAdjustments.buttonSize.minW,
    iconSize: orientationAdjustments.buttonSize.iconSize
  });
  
  // Logo styles
  const getLogoStyles = () => ({
    width: `${logoSize.size}px`,
    height: `${logoSize.size}px`,
    containerSize: `${logoSize.containerSize}px`
  });
  
  // Responsive breakpoint values for Chakra UI
  const getResponsiveBreakpoints = () => ({
    // Logo visibility
    logoOpacity: {
      base: isLogoVisible ? 1 : 0,
      '4xl': 1 // Always visible on tablet+
    },
    
    // Logo transform
    logoTransform: {
      base: `translateX(-50%) ${isLogoVisible ? 'translateY(0)' : 'translateY(-100px)'}`,
      '4xl': 'translateX(0)'
    },
    
    // Logo position
    logoPosition: {
      base: shouldUseScrollBehavior() ? getLogoPosition() : {
        position: 'static',
        top: 'auto',
        left: 'auto',
        transform: 'none',
        opacity: 1,
        transition: 'none'
      },
      '4xl': { position: 'static', top: 'auto', left: 'auto', transform: 'none' }
    },
    
    // Navigation direction - use bottom nav for all mobile devices
    navDirection: {
      base: shouldUseBottomNavigation() ? 'row' : 'column'
    },

    // Navigation position
    navPosition: {
      base: getNavigationPosition()
    },
    
    // Button sizes
    buttonSize: {
      base: getButtonStyles().minHeight,
      xs: '36px',
      sm: '40px',
      md: '44px',
      lg: '48px',
      xl: '50px',
      '2xl': '52px',
      '3xl': '54px',
      '4xl': '48px'
    },
    
    // Icon sizes
    iconSize: {
      base: orientationAdjustments.buttonSize.iconSize,
      xs: 18,
      sm: 20,
      md: 22,
      lg: 24,
      xl: 26,
      '2xl': 28,
      '3xl': 30,
      '4xl': 28
    },
    
    // Spacing
    spacing: {
      base: orientationAdjustments.spacing,
      xs: 6,
      sm: 8,
      md: 10,
      lg: 12,
      xl: 14,
      '2xl': 16,
      '3xl': 18,
      '4xl': 20
    }
  });
  
  return {
    // Device info
    deviceCategory,
    isMobile: isMobileDevice(),
    orientation,
    hasNotch: hasNotch(),
    
    // Dynamic values
    spacing,
    buttonSize,
    logoSize,
    safeArea,
    
    // State
    isLogoVisible,
    
    // Positioning functions
    getLogoPosition,
    getNavigationPosition,
    getButtonStyles,
    getLogoStyles,
    
    // Chakra UI responsive values
    responsive: getResponsiveBreakpoints(),
    
    // Utility functions
    updateDeviceCategory
  };
};
